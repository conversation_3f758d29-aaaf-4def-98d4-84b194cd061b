/* pages/index/index.wxss */
.header {
  padding: 30rpx 0;
  position: relative;
}

.security-notice {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.settings-button {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.settings-icon {
  font-size: 32rpx;
}

.security-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
  color: #007AFF;
}

.security-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.search-container {
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  position: relative;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
}

.password-list {
  margin-top: 20rpx;
}

.password-item {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.site-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.username {
  font-size: 28rpx;
  color: #666;
}

.arrow {
  color: #999;
  font-size: 36rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  color: #999;
  font-size: 32rpx;
}

.add-button {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #007AFF;
  color: white;
  height: 100rpx;
  border-radius: 50rpx;
  padding: 0 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0, 123, 255, 0.3);
}

.add-icon {
  font-size: 44rpx;
  margin-right: 10rpx;
}

.add-text {
  font-size: 32rpx;
}

/* 手势密码认证样式 */
.auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 30rpx;
  margin-top: 40rpx;
}

.auth-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

.auth-gesture-canvas {
  width: 500rpx;
  height: 500rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.auth-error-message {
  color: #FF3B30;
  font-size: 28rpx;
  margin: 20rpx 0;
  text-align: center;
}

.auth-hint {
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}
