<!--pages/settings/settings.wxml-->
<view class="page">
  <view class="header">
    <view class="title">设置</view>
  </view>

  <view class="settings-container">
    <!-- 隐私设置 -->
    <view class="settings-section">
      <view class="section-title">隐私设置</view>

      <view class="setting-item">
        <view class="setting-label">
          <view class="label-text">隐藏密码记录</view>
          <view class="label-desc">开启后需要手势密码验证才能查看密码列表</view>
        </view>
        <switch checked="{{hidePasswordList}}" bindchange="handleHidePasswordListChange" color="#007AFF" />
      </view>

      <view class="setting-item" wx:if="{{hidePasswordList}}">
        <view class="setting-label">
          <view class="label-text">设置访问手势密码</view>
          <view class="label-desc">用于解锁密码列表的手势密码</view>
        </view>
        <button class="btn-secondary" bindtap="setAccessGesture">
          {{accessGestureSet ? '重新设置' : '设置手势密码'}}
        </button>
      </view>
    </view>

    <!-- 密码类型设置 -->
    <view class="settings-section">
      <view class="section-title">密码管理</view>

      <view class="setting-item">
        <view class="setting-label">
          <view class="label-text">允许保存实际密码</view>
          <view class="label-desc">开启后可以直接保存现有密码，而不仅仅是生成密码</view>
        </view>
        <switch checked="{{allowSaveActualPassword}}" bindchange="handleAllowSaveActualPasswordChange" color="#007AFF" />
      </view>
    </view>

    <!-- 手势密码设置弹窗 -->
    <view class="gesture-modal" wx:if="{{showGestureModal}}">
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">{{isConfirmingAccess ? '请再次输入手势密码以确认' : '请设置访问手势密码'}}</view>
        </view>

        <view class="gesture-container">
          <!-- Gesture Pattern Canvas -->
          <canvas type="2d" id="access-gesture-canvas" class="gesture-canvas"
                  disable-scroll="true"
                  bindtouchstart="handleTouchStart"
                  bindtouchmove="handleTouchMove"
                  bindtouchend="handleTouchEnd">
          </canvas>

          <view wx:if="{{showError}}" class="error-message">{{errorMessage}}</view>
          <view wx:if="{{accessGestureComplete}}" class="success-message">访问手势密码设置成功</view>

          <view class="gesture-hint">请连接至少 4 个点</view>
        </view>

        <view class="modal-actions">
          <button class="btn-secondary" bindtap="cancelGestureSetup">取消</button>
          <button class="btn-primary" wx:if="{{accessGestureComplete}}" bindtap="confirmGestureSetup">确认</button>
        </view>
      </view>
    </view>
  </view>
</view>