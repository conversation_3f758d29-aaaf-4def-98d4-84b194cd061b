/* pages/add/add.wxss */
.header {
  padding: 30rpx 0;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.form-container {
  padding: 0 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.input-item {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.uri-removed-tip {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  padding: 10rpx;
  background-color: #e5f3ff;
  border-radius: 6rpx;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #0076ff;
  line-height: 1.3;
}

.gesture-container {
  margin: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gesture-title {
  font-size: 32rpx;
  margin-bottom: 30rpx;
  text-align: center;
  color: #333;
}

.gesture-canvas {
  width: 600rpx;
  height: 600rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.error-message {
  color: #FF3B30;
  font-size: 28rpx;
  margin: 20rpx 0;
  text-align: center;
}

.success-message {
  color: #34C759;
  font-size: 28rpx;
  margin: 20rpx 0;
  text-align: center;
}

.gesture-hint {
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.reset-button {
  margin-top: 20rpx;
  color: #007AFF;
  font-size: 28rpx;
}

.security-explanation {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 40rpx 0 20rpx;
}

.security-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.security-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
  color: #007AFF;
}

.security-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.btn-container {
  margin: 40rpx 0;
  display: flex;
  flex-direction: column;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  font-size: 32rpx;
}

.btn-secondary {
  background-color: #f8f8f8;
  color: #333;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  margin-top: 20rpx;
  font-size: 32rpx;
}

.btn-disabled {
  background-color: #CCCCCC;
  opacity: 0.7;
}

.password-type-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.password-type-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333;
}

.picker-item {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.password-section {
  margin: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.display-password {
  background-color: #f0f0f0;
  padding: 30rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  font-family: monospace;
  font-size: 34rpx;
  letter-spacing: 2rpx;
  width: 100%;
  text-align: center;
  word-break: break-all;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.password-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 20rpx;
}

.btn-action {
  background-color: #007AFF;
  color: white;
  border-radius: 8rpx;
  padding: 16rpx 0;
  text-align: center;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.btn-reset {
  background-color: #f8f8f8;
  color: #333;
}

/* 密码模式选择样式 */
.password-mode-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.password-mode-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333;
}

/* 现有密码输入样式 */
.form-group {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.toggle-icon {
  font-size: 28rpx;
}

/* 保存密码信息样式 */
.saved-password-info {
  display: flex;
  align-items: center;
  background-color: #f0f9ff;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #007AFF;
}

.info-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.info-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}
