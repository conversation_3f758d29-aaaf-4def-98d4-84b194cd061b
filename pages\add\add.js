// pages/add/add.js
Page({
  data: {
    site: '',
    username: '',
    passwordLength: 16,
    passwordType: 'strong', // Default to strong password
    passwordTypeIndex: 0, // Default index for dropdown
    passwordTypes: [
      { value: 'strong', label: '强密码' },
      { value: 'alphanumeric', label: '字母数字密码' },
      { value: 'alphabetic', label: '纯字母密码' },
      { value: 'numeric', label: '纯数字密码' }
    ],
    passwordMode: 'generate', // 'generate' or 'save'
    allowSaveActualPassword: false,
    existingPassword: '',
    showExistingPassword: false,
    isConfirming: false,
    gesturePassword: [],
    confirmGesturePassword: [],
    lastPoint: null,
    currentLineColor: '#007AFF',
    gestureComplete: false,
    gridSize: 3,
    pointRadius: 20,
    canvasWidth: 300,
    canvasHeight: 300,
    points: [],
    drawnLines: [],
    showError: false,
    errorMessage: '',
    generatedPassword: '', // To store the generated password
    showPassword: false, // To control password display
    showSavedPassword: false // To control saved password display
  },

  onLoad: function(options) {
    this.loadSettings();
    this.initCanvas();
  },

  loadSettings: function() {
    try {
      const settings = wx.getStorageSync('appSettings') || {};
      this.setData({
        allowSaveActualPassword: settings.allowSaveActualPassword || false
      });
    } catch (e) {
      console.error('Failed to load settings:', e);
    }
  },

  onReady: function() {
    // Initialize the canvas when ready
    const query = wx.createSelectorQuery();
    query.select('#gesture-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 获取canvas的实际渲染尺寸
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = this.data.canvasWidth * dpr;
        canvas.height = this.data.canvasHeight * dpr;

        // 设置canvas的样式尺寸
        canvas.style = {
          width: this.data.canvasWidth + 'px',
          height: this.data.canvasHeight + 'px'
        };

        // 缩放绘图上下文，使得1个单位等于1个物理像素
        ctx.scale(dpr, dpr);

        this.canvas = canvas;
        this.ctx = ctx;

        // Initialize the grid points
        this.initializePoints();
        this.drawGestureGrid();
      });
  },

  initCanvas: function() {
    // Calculate canvas dimensions
    const windowWidth = wx.getSystemInfoSync().windowWidth;
    const canvasSize = windowWidth * 0.8; // Canvas will be 80% of screen width

    this.setData({
      canvasWidth: canvasSize,
      canvasHeight: canvasSize
    });

    // 在页面显示后，确保canvas已经渲染完成
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.select('#gesture-canvas')
        .boundingClientRect()
        .exec((res) => {
          if (res && res[0]) {
            console.log('Canvas position:', res[0]);
          }
        });
    }, 300);
  },

  initializePoints: function() {
    const { gridSize, canvasWidth, canvasHeight, pointRadius } = this.data;
    const points = [];

    // Calculate the space between points
    const spacingX = canvasWidth / gridSize;
    const spacingY = canvasHeight / gridSize;

    // Calculate the offset to center the points
    const offsetX = spacingX / 2;
    const offsetY = spacingY / 2;

    // Create the grid of points
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        points.push({
          x: offsetX + j * spacingX,
          y: offsetY + i * spacingY,
          id: i * gridSize + j + 1, // Point ID (1-9)
          touched: false
        });
      }
    }

    this.setData({ points });
  },

  drawGestureGrid: function() {
    const { points, pointRadius, drawnLines, currentLineColor } = this.data;
    const ctx = this.ctx;

    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

    // Draw the grid points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, pointRadius, 0, Math.PI * 2);
      ctx.fillStyle = point.touched ? currentLineColor : '#E0E0E0';
      ctx.fill();
      ctx.strokeStyle = '#CCCCCC';
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Draw the connecting lines between touched points
    if (drawnLines.length > 0) {
      ctx.beginPath();
      ctx.moveTo(drawnLines[0].x, drawnLines[0].y);

      for (let i = 1; i < drawnLines.length; i++) {
        ctx.lineTo(drawnLines[i].x, drawnLines[i].y);
      }

      // If there is a last point that is not part of the drawn lines yet
      if (this.data.lastPoint) {
        ctx.lineTo(this.data.lastPoint.x, this.data.lastPoint.y);
      }

      ctx.strokeStyle = currentLineColor;
      ctx.lineWidth = 5;
      ctx.stroke();
    }
  },

  handleTouchStart: function(e) {
    if (this.data.gestureComplete) return;

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Check if the touch started on a point
        const point = this.findTouchedPoint(x, y);

        if (point) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Start drawing lines
          const drawnLines = [{ x: point.x, y: point.y }];

          // Add the point to gesture password
          const gesturePassword = this.data.isConfirming
            ? [...this.data.confirmGesturePassword, point.id]
            : [...this.data.gesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            lastPoint: null,
            ...(this.data.isConfirming
              ? { confirmGesturePassword: gesturePassword }
              : { gesturePassword })
          });

          this.drawGestureGrid();
        }
      });
  },

  handleTouchMove: function(e) {
    if (this.data.gestureComplete) return;
    if (this.data.drawnLines.length === 0) return; // No touch start yet

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Update last point for smooth line drawing
        this.setData({
          lastPoint: { x: x, y: y }
        });

        // Check if we've touched a new point
        const point = this.findTouchedPoint(x, y);

        if (point && !this.isPointAlreadyTouched(point.id)) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Add to drawn lines
          const drawnLines = [...this.data.drawnLines, { x: point.x, y: point.y }];

          // Add the point to gesture password
          const gesturePassword = this.data.isConfirming
            ? [...this.data.confirmGesturePassword, point.id]
            : [...this.data.gesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            ...(this.data.isConfirming
              ? { confirmGesturePassword: gesturePassword }
              : { gesturePassword })
          });
        }

        this.drawGestureGrid();
      });
  },

  handleTouchEnd: function() {
    if (this.data.gestureComplete) return;

    // Check if the gesture is valid (at least 4 points)
    const passwordArray = this.data.isConfirming
      ? this.data.confirmGesturePassword
      : this.data.gesturePassword;

    if (passwordArray.length < 4) {
      this.setData({
        showError: true,
        errorMessage: '手势密码至少需要连接 4 个点',
        currentLineColor: '#FF3B30'
      });

      this.drawGestureGrid();

      // Reset after a delay
      setTimeout(() => {
        this.resetGesture();
      }, 1500);

      return;
    }

    // If we're confirming the gesture
    if (this.data.isConfirming) {
      // Check if the confirmation matches the original gesture
      if (this.arraysEqual(this.data.gesturePassword, this.data.confirmGesturePassword)) {
        // Generate the password using the site, username, gesture password, and password type
        const generatedPassword = this.generatePassword(
          this.data.site,
          this.data.username,
          this.data.gesturePassword,
          this.data.passwordLength,
          this.data.passwordType
        );

        // Keep the touched points visible when showing the password
        const updatedPoints = this.data.points.map(p => {
          if (this.data.confirmGesturePassword.includes(p.id)) {
            return { ...p, touched: true };
          }
          return p;
        });

        this.setData({
          gestureComplete: true,
          currentLineColor: '#34C759',
          showError: false,
          generatedPassword: generatedPassword,
          showPassword: true,
          points: updatedPoints
        });

        this.drawGestureGrid();

        // Alert the user that the gesture was set successfully
        wx.showToast({
          title: '手势密码设置成功',
          icon: 'success'
        });

        // Enable the save button
      } else {
        // Gestures don't match
        this.setData({
          showError: true,
          errorMessage: '手势密码不匹配，请重试',
          currentLineColor: '#FF3B30'
        });

        this.drawGestureGrid();

        // Reset after a delay
        setTimeout(() => {
          this.resetGesture();
          this.setData({
            isConfirming: false,
            gesturePassword: [],
            confirmGesturePassword: []
          });
        }, 1500);
      }
    } else {
      // First time setting the gesture, now ask to confirm
      this.setData({
        isConfirming: true,
        showError: false
      });

      // Reset the canvas for confirmation
      setTimeout(() => {
        this.resetGesture();
      }, 500);
    }
  },

  generatePassword: function(site, username, gestureArray, length, passwordType = 'strong') {
    // Step 1: Create a seed by combining site, username, and gesture sequence
    const gestureSeed = gestureArray.join('');
    const inputSeed = `${site}:${username}:${gestureSeed}`;

    // Step 2: Generate a hash using a simple algorithm (since we can't use crypto libraries)
    // This is a simple hash function that should be deterministic for the same input
    const hash = this.simpleHash(inputSeed);

    // Step 3: Define character sets
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const special = '!@#$%^&*()_+-=[]{}|;:\',./\<\>?';

    // Define character sets based on password type
    let charSet = '';
    let requiredChars = [];

    switch (passwordType) {
      case 'numeric':
        // Only numbers
        charSet = numbers;
        // Add at least one number
        requiredChars.push(numbers[this.getPositionFromHash(hash, 0, numbers.length)]);
        break;

      case 'alphabetic':
        // Only letters (lowercase and uppercase)
        charSet = lowercase + uppercase;
        // Add at least one lowercase and one uppercase
        requiredChars.push(lowercase[this.getPositionFromHash(hash, 0, lowercase.length)]);
        requiredChars.push(uppercase[this.getPositionFromHash(hash, 1, uppercase.length)]);
        break;

      case 'alphanumeric':
        // Letters and numbers
        charSet = lowercase + uppercase + numbers;
        // Add at least one lowercase, one uppercase, and one number
        requiredChars.push(lowercase[this.getPositionFromHash(hash, 0, lowercase.length)]);
        requiredChars.push(uppercase[this.getPositionFromHash(hash, 1, uppercase.length)]);
        requiredChars.push(numbers[this.getPositionFromHash(hash, 2, numbers.length)]);
        break;

      case 'strong':
      default:
        // All characters (default)
        charSet = lowercase + uppercase + numbers + special;
        // Add at least one from each set
        requiredChars.push(lowercase[this.getPositionFromHash(hash, 0, lowercase.length)]);
        requiredChars.push(uppercase[this.getPositionFromHash(hash, 1, uppercase.length)]);
        requiredChars.push(numbers[this.getPositionFromHash(hash, 2, numbers.length)]);
        requiredChars.push(special[this.getPositionFromHash(hash, 3, special.length)]);
        break;
    }

    // Start with required characters
    let password = requiredChars.join('');

    // Fill the rest of the password to reach desired length
    for (let i = requiredChars.length; i < length; i++) {
      const position = this.getPositionFromHash(hash, i, charSet.length);
      password += charSet[position];
    }

    // Shuffle the password to avoid predictable patterns
    return this.shuffleString(password, hash);
  },

  simpleHash: function(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString();
  },

  getPositionFromHash: function(hash, index, max) {
    // Use different parts of the hash to get positions
    const hashPart = parseInt(hash.substring(index % hash.length, (index % hash.length) + 5));
    return hashPart % max;
  },

  shuffleString: function(str, seed) {
    const array = str.split('');
    const seedNum = parseInt(seed);

    // Fisher-Yates shuffle algorithm with seed
    for (let i = array.length - 1; i > 0; i--) {
      // Use the seed and the current index to generate a "random" index
      const j = Math.floor((seedNum % (i + 1) + i + 1) % (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }

    return array.join('');
  },

  findTouchedPoint: function(x, y) {
    const { points, pointRadius } = this.data;

    // Check if the touch is within any point's radius
    for (const point of points) {
      const distance = Math.sqrt(
        Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)
      );

      if (distance <= pointRadius * 1.5) { // A bit larger area for easier touch
        return point;
      }
    }

    return null;
  },

  isPointAlreadyTouched: function(pointId) {
    const passwordArray = this.data.isConfirming
      ? this.data.confirmGesturePassword
      : this.data.gesturePassword;

    return passwordArray.includes(pointId);
  },

  resetGesture: function() {
    // Reset the grid points
    const updatedPoints = this.data.points.map(p => ({ ...p, touched: false }));

    this.setData({
      points: updatedPoints,
      drawnLines: [],
      lastPoint: null,
      showError: false,
      currentLineColor: '#007AFF',
      ...(this.data.isConfirming ? {} : { gesturePassword: [] }),
      ...(this.data.isConfirming ? { confirmGesturePassword: [] } : {})
    });

    // Reinitialize canvas if needed
    if (!this.ctx) {
      const query = wx.createSelectorQuery();
      query.select('#gesture-canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res && res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            // Set canvas width and height
            canvas.width = this.data.canvasWidth;
            canvas.height = this.data.canvasHeight;

            this.canvas = canvas;
            this.ctx = ctx;

            // Initialize the grid points
            this.initializePoints();
            this.drawGestureGrid();
          }
        });
    } else {
      this.drawGestureGrid();
    }
  },

  arraysEqual: function(a, b) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (a[i] !== b[i]) return false;
    }
    return true;
  },

  resetAll: function() {
    this.setData({
      isConfirming: false,
      gesturePassword: [],
      confirmGesturePassword: [],
      gestureComplete: false,
      showPassword: false,
      generatedPassword: ''
    });

    this.resetGesture();
  },

  copyPassword: function() {
    const { generatedPassword } = this.data;

    wx.setClipboardData({
      data: generatedPassword,
      success: function() {
        wx.showToast({
          title: '密码已复制',
          icon: 'success'
        });
      }
    });
  },

  handleSiteInput: function(e) {
    const inputValue = e.detail.value;

    // Check if the input looks like a URL with URI
    if (this.isUrl(inputValue)) {
      const cleanedUrl = this.extractDomain(inputValue);

      // If we cleaned the URL (removed URI parts)
      if (cleanedUrl !== inputValue) {
        this.setData({
          site: cleanedUrl,
          showUriRemovedTip: true
        });

        // Show toast notification
        wx.showToast({
          title: '已自动移除URI部分',
          icon: 'none',
          duration: 2000
        });

        // Hide the tip after a delay
        setTimeout(() => {
          this.setData({ showUriRemovedTip: false });
        }, 3000);
      } else {
        this.setData({ site: inputValue });
      }
    } else {
      this.setData({ site: inputValue });
    }
  },

  isUrl: function(str) {
    // Simple check if string might be a URL
    return /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$/.test(str);
  },

  extractDomain: function(url) {
    // Add protocol if missing for URL parsing
    if (!/^https?:\/\//.test(url)) {
      url = 'http://' + url;
    }

    try {
      // Parse the URL and extract hostname
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (e) {
      // If URL parsing fails, try a simple regex approach
      const match = url.match(/^(?:https?:\/\/)?([\w-]+(?:\.[\w-]+)+)/i);
      return match ? match[1] : url;
    }
  },

  handleUsernameInput: function(e) {
    this.setData({ username: e.detail.value });
  },

  handleLengthChange: function(e) {
    this.setData({ passwordLength: e.detail.value });
  },

  handlePasswordTypeChange: function(e) {
    const index = e.detail.value;
    const selectedType = this.data.passwordTypes[index].value;
    this.setData({
      passwordTypeIndex: index,
      passwordType: selectedType
    });
  },

  savePasswordEntry: function() {
    const { site, username, passwordLength, passwordType, gesturePassword } = this.data;

    // Validate input
    if (!site) {
      wx.showToast({
        title: '请输入网站名称',
        icon: 'none'
      });
      return;
    }

    if (!username) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }

    if (!gesturePassword || gesturePassword.length < 4) {
      wx.showToast({
        title: '请设置手势密码',
        icon: 'none'
      });
      return;
    }

    try {
      // Get existing entries
      const passwordEntries = wx.getStorageSync('passwordEntries') || [];

      // Check for duplicate entries (same site + username)
      const isDuplicate = passwordEntries.some(entry =>
        entry.site.toLowerCase() === site.toLowerCase() &&
        entry.username.toLowerCase() === username.toLowerCase()
      );

      if (isDuplicate) {
        wx.showModal({
          title: '重复条目',
          content: '已存在相同网站/标签和用户名的密码条目，是否继续添加？',
          success: (res) => {
            if (res.confirm) {
              // User confirmed, proceed with saving
              this.saveNewEntry(passwordEntries);
            }
          }
        });
      } else {
        // No duplicate, proceed with saving
        this.saveNewEntry(passwordEntries);
      }
    } catch (e) {
      console.error('Failed to save password entry:', e);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  saveNewEntry: function(passwordEntries) {
    const { site, username, passwordLength, passwordType, gesturePassword } = this.data;

    // Generate a unique ID
    const id = Date.now().toString();

    // Create new password entry
    const newEntry = {
      id,
      site,
      username,
      length: passwordLength,
      passwordType: passwordType // Save the password type
    };

    // Add new entry
    passwordEntries.push(newEntry);

    // Save to storage
    wx.setStorageSync('passwordEntries', passwordEntries);

    // Show success message
    wx.showToast({
      title: '密码已保存',
      icon: 'success'
    });

    // Navigate back to list
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
});
