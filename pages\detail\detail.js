// pages/detail/detail.js
Page({
  data: {
    id: '',
    site: '',
    username: '',
    passwordLength: 16,
    passwordEntry: null,
    gesturePassword: [],
    lastPoint: null,
    currentLineColor: '#007AFF',
    gridSize: 3,
    pointRadius: 20,
    canvasWidth: 300,
    canvasHeight: 300,
    points: [],
    drawnLines: [],
    generatedPassword: '',
    showPassword: false,
    verificationComplete: false
  },

  onLoad: function(options) {
    // Get password entry ID from options
    if (options.id) {
      this.setData({ id: options.id });
      this.loadPasswordEntry(options.id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }

    this.initCanvas();
  },

  onReady: function() {
    // Initialize the canvas when ready
    const query = wx.createSelectorQuery();
    query.select('#gesture-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 获取canvas的实际渲染尺寸
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = this.data.canvasWidth * dpr;
        canvas.height = this.data.canvasHeight * dpr;

        // 设置canvas的样式尺寸
        canvas.style = {
          width: this.data.canvasWidth + 'px',
          height: this.data.canvasHeight + 'px'
        };

        // 缩放绘图上下文，使得1个单位等于1个物理像素
        ctx.scale(dpr, dpr);

        this.canvas = canvas;
        this.ctx = ctx;

        // Initialize the grid points
        this.initializePoints();
        this.drawGestureGrid();

        // Store canvas position for touch coordinate calculation
        setTimeout(() => {
          const posQuery = wx.createSelectorQuery();
          posQuery.select('#gesture-canvas')
            .boundingClientRect()
            .exec((posRes) => {
              if (posRes && posRes[0]) {
                this.canvasPosition = posRes[0];
                console.log('Canvas position stored:', this.canvasPosition);
              }
            });
        }, 100);
      });
  },

  loadPasswordEntry: function(id) {
    try {
      const passwordEntries = wx.getStorageSync('passwordEntries') || [];
      const entry = passwordEntries.find(item => item.id === id);

      if (entry) {
        this.setData({
          passwordEntry: entry,
          site: entry.site,
          username: entry.username,
          passwordLength: entry.length
        });
      } else {
        wx.showToast({
          title: '密码条目不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (e) {
      console.error('Failed to load password entry:', e);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  initCanvas: function() {
    // Calculate canvas dimensions
    const windowWidth = wx.getSystemInfoSync().windowWidth;
    const canvasSize = windowWidth * 0.8; // Canvas will be 80% of screen width

    this.setData({
      canvasWidth: canvasSize,
      canvasHeight: canvasSize
    });

    // 在页面显示后，确保canvas已经渲染完成
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.select('#gesture-canvas')
        .boundingClientRect()
        .exec((res) => {
          if (res && res[0]) {
            console.log('Canvas position:', res[0]);
            // Store canvas position for later use
            this.canvasPosition = res[0];
          }
        });
    }, 300);
  },

  initializePoints: function() {
    const { gridSize, canvasWidth, canvasHeight, pointRadius } = this.data;
    const points = [];

    // Calculate the space between points
    const spacingX = canvasWidth / gridSize;
    const spacingY = canvasHeight / gridSize;

    // Calculate the offset to center the points
    const offsetX = spacingX / 2;
    const offsetY = spacingY / 2;

    // Create the grid of points
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        points.push({
          x: offsetX + j * spacingX,
          y: offsetY + i * spacingY,
          id: i * gridSize + j + 1, // Point ID (1-9)
          touched: false
        });
      }
    }

    this.setData({ points });

    // Log the points for debugging
    console.log('Initialized points:', points);
  },

  drawGestureGrid: function() {
    const { points, pointRadius, drawnLines, currentLineColor } = this.data;
    const ctx = this.ctx;

    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

    // Draw the grid points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, pointRadius, 0, Math.PI * 2);
      ctx.fillStyle = point.touched ? currentLineColor : '#E0E0E0';
      ctx.fill();
      ctx.strokeStyle = '#CCCCCC';
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Draw the connecting lines between touched points
    if (drawnLines.length > 0) {
      ctx.beginPath();
      ctx.moveTo(drawnLines[0].x, drawnLines[0].y);

      for (let i = 1; i < drawnLines.length; i++) {
        ctx.lineTo(drawnLines[i].x, drawnLines[i].y);
      }

      // If there is a last point that is not part of the drawn lines yet
      if (this.data.lastPoint) {
        ctx.lineTo(this.data.lastPoint.x, this.data.lastPoint.y);
      }

      ctx.strokeStyle = currentLineColor;
      ctx.lineWidth = 5;
      ctx.stroke();
    }
  },

  handleTouchStart: function(e) {
    if (this.data.verificationComplete) return;

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Check if the touch started on a point
        const point = this.findTouchedPoint(x, y);

        if (point) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Start drawing lines
          const drawnLines = [{ x: point.x, y: point.y }];

          // Add the point to gesture password
          const gesturePassword = [...this.data.gesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            lastPoint: null,
            gesturePassword
          });

          this.drawGestureGrid();
        }
      });
  },

  handleTouchMove: function(e) {
    if (this.data.verificationComplete) return;
    if (this.data.drawnLines.length === 0) return; // No touch start yet

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Update last point for smooth line drawing
        this.setData({
          lastPoint: { x: x, y: y }
        });

        // Check if we've touched a new point
        const point = this.findTouchedPoint(x, y);

        if (point && !this.isPointAlreadyTouched(point.id)) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Add to drawn lines
          const drawnLines = [...this.data.drawnLines, { x: point.x, y: point.y }];

          // Add the point to gesture password
          const gesturePassword = [...this.data.gesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            gesturePassword
          });
        }

        this.drawGestureGrid();
      });
  },

  handleTouchEnd: function() {
    if (this.data.verificationComplete) return;

    const { gesturePassword, passwordEntry } = this.data;

    // Check if gesture is valid (at least 4 points)
    if (gesturePassword.length < 4) {
      wx.showToast({
        title: '手势密码至少需要连接 4 个点',
        icon: 'none'
      });

      setTimeout(() => {
        this.resetGesture();
      }, 1000);

      return;
    }

    // Verify gesture password matches the stored one
    if (!this.arraysEqual(gesturePassword, passwordEntry.gesturePassword)) {
      wx.showToast({
        title: '手势密码错误',
        icon: 'none'
      });

      setTimeout(() => {
        this.resetGesture();
      }, 1000);

      return;
    }

    let displayPassword = '';

    if (passwordEntry.passwordMode === 'save') {
      // Decrypt the saved password
      displayPassword = this.decryptPassword(passwordEntry.encryptedPassword, gesturePassword);
    } else {
      // Generate the password using the site, username, gesture password, and password type
      const passwordType = passwordEntry.passwordType || 'strong';
      displayPassword = this.generatePassword(
        this.data.site,
        this.data.username,
        this.data.gesturePassword,
        this.data.passwordLength,
        passwordType
      );
    }

    // Keep the touched points visible when showing the password
    const updatedPoints = this.data.points.map(p => {
      if (this.data.gesturePassword.includes(p.id)) {
        return { ...p, touched: true };
      }
      return p;
    });

    this.setData({
      generatedPassword: displayPassword,
      showPassword: true,
      verificationComplete: true,
      currentLineColor: '#34C759',
      points: updatedPoints,
      lastPoint: null // 清除最后一个点，避免绘制不必要的线
    });

    this.drawGestureGrid();

    // 显示成功提示
    wx.showToast({
      title: passwordEntry.passwordMode === 'save' ? '密码已解密' : '密码已生成',
      icon: 'success',
      duration: 1500
    });
  },

  generatePassword: function(site, username, gestureArray, length, passwordType = 'strong') {
    // Step 1: Create a seed by combining site, username, and gesture sequence
    const gestureSeed = gestureArray.join('');
    const inputSeed = `${site}:${username}:${gestureSeed}`;

    // Step 2: Generate a hash using a simple algorithm (since we can't use crypto libraries)
    // This is a simple hash function that should be deterministic for the same input
    const hash = this.simpleHash(inputSeed);

    // Step 3: Define character sets
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const special = '!@#$%^&*()_+-=[]{}|;:\',./\<\>?';

    // Define character sets based on password type
    let charSet = '';
    let requiredChars = [];

    switch (passwordType) {
      case 'numeric':
        // Only numbers
        charSet = numbers;
        // Add at least one number
        requiredChars.push(numbers[this.getPositionFromHash(hash, 0, numbers.length)]);
        break;

      case 'alphabetic':
        // Only letters (lowercase and uppercase)
        charSet = lowercase + uppercase;
        // Add at least one lowercase and one uppercase
        requiredChars.push(lowercase[this.getPositionFromHash(hash, 0, lowercase.length)]);
        requiredChars.push(uppercase[this.getPositionFromHash(hash, 1, uppercase.length)]);
        break;

      case 'alphanumeric':
        // Letters and numbers
        charSet = lowercase + uppercase + numbers;
        // Add at least one lowercase, one uppercase, and one number
        requiredChars.push(lowercase[this.getPositionFromHash(hash, 0, lowercase.length)]);
        requiredChars.push(uppercase[this.getPositionFromHash(hash, 1, uppercase.length)]);
        requiredChars.push(numbers[this.getPositionFromHash(hash, 2, numbers.length)]);
        break;

      case 'strong':
      default:
        // All characters (default)
        charSet = lowercase + uppercase + numbers + special;
        // Add at least one from each set
        requiredChars.push(lowercase[this.getPositionFromHash(hash, 0, lowercase.length)]);
        requiredChars.push(uppercase[this.getPositionFromHash(hash, 1, uppercase.length)]);
        requiredChars.push(numbers[this.getPositionFromHash(hash, 2, numbers.length)]);
        requiredChars.push(special[this.getPositionFromHash(hash, 3, special.length)]);
        break;
    }

    // Start with required characters
    let password = requiredChars.join('');

    // Fill the rest of the password to reach desired length
    for (let i = requiredChars.length; i < length; i++) {
      const position = this.getPositionFromHash(hash, i, charSet.length);
      password += charSet[position];
    }

    // Shuffle the password to avoid predictable patterns
    return this.shuffleString(password, hash);
  },

  simpleHash: function(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString();
  },

  getPositionFromHash: function(hash, index, max) {
    // Use different parts of the hash to get positions
    const hashPart = parseInt(hash.substring(index % hash.length, (index % hash.length) + 5));
    return hashPart % max;
  },

  shuffleString: function(str, seed) {
    const array = str.split('');
    const seedNum = parseInt(seed);

    // Fisher-Yates shuffle algorithm with seed
    for (let i = array.length - 1; i > 0; i--) {
      // Use the seed and the current index to generate a "random" index
      const j = Math.floor((seedNum % (i + 1) + i + 1) % (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }

    return array.join('');
  },

  findTouchedPoint: function(x, y) {
    const { points, pointRadius } = this.data;

    // Check if the touch is within any point's radius
    for (const point of points) {
      const distance = Math.sqrt(
        Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)
      );

      if (distance <= pointRadius * 2) { // Increased touch area for better detection
        return point;
      }
    }

    return null;
  },

  isPointAlreadyTouched: function(pointId) {
    return this.data.gesturePassword.includes(pointId);
  },

  resetGesture: function() {
    // Reset the grid points
    const updatedPoints = this.data.points.map(p => ({ ...p, touched: false }));

    this.setData({
      points: updatedPoints,
      drawnLines: [],
      lastPoint: null,
      gesturePassword: [],
      showPassword: false,
      verificationComplete: false,
      currentLineColor: '#007AFF'
    });

    // Redraw the grid without reinitializing the canvas
    this.drawGestureGrid();

  },

  copyPassword: function() {
    const { generatedPassword } = this.data;

    wx.setClipboardData({
      data: generatedPassword,
      success: function() {
        wx.showToast({
          title: '密码已复制',
          icon: 'success'
        });
      }
    });
  },

  deleteEntry: function() {
    const { id } = this.data;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个密码条目吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // Get existing password entries
            const passwordEntries = wx.getStorageSync('passwordEntries') || [];

            // Filter out the entry to delete
            const updatedEntries = passwordEntries.filter(item => item.id !== id);

            // Save updated entries back to storage
            wx.setStorageSync('passwordEntries', updatedEntries);

            wx.showToast({
              title: '已删除',
              icon: 'success'
            });

            // Navigate back to list
            setTimeout(() => {
              wx.navigateBack();
            }, 1000);
          } catch (e) {
            console.error('Failed to delete password entry:', e);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // Simple decryption function using gesture password as key
  decryptPassword: function(encryptedPassword, gesturePassword) {
    try {
      const key = gesturePassword.join('');
      // Base64 decode first
      const encrypted = new TextDecoder().decode(wx.base64ToArrayBuffer(encryptedPassword));

      let decrypted = '';
      for (let i = 0; i < encrypted.length; i++) {
        const charCode = encrypted.charCodeAt(i);
        const keyChar = key.charCodeAt(i % key.length);
        decrypted += String.fromCharCode(charCode ^ keyChar);
      }

      return decrypted;
    } catch (e) {
      console.error('Failed to decrypt password:', e);
      return '解密失败';
    }
  },

  arraysEqual: function(a, b) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (a[i] !== b[i]) return false;
    }
    return true;
  }
});
