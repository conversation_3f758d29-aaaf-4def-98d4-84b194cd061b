// pages/settings/settings.js
Page({
  data: {
    hidePasswordList: false,
    allowSaveActualPassword: false,
    accessGestureSet: false,
    showGestureModal: false,
    isConfirmingAccess: false,
    accessGesturePassword: [],
    confirmAccessGesturePassword: [],
    accessGestureComplete: false,

    // 手势密码相关
    lastPoint: null,
    currentLineColor: '#007AFF',
    gridSize: 3,
    pointRadius: 15,
    canvasWidth: 200,
    canvasHeight: 200,
    points: [],
    drawnLines: [],
    showError: false,
    errorMessage: ''
  },

  onLoad: function(options) {
    this.loadSettings();
    this.initCanvas();
  },

  onReady: function() {
    // Canvas will be initialized when modal is shown
  },

  loadSettings: function() {
    try {
      const settings = wx.getStorageSync('appSettings') || {};
      this.setData({
        hidePasswordList: settings.hidePasswordList || false,
        allowSaveActualPassword: settings.allowSaveActualPassword || false,
        accessGestureSet: !!(settings.accessGesturePassword && settings.accessGesturePassword.length > 0)
      });
    } catch (e) {
      console.error('Failed to load settings:', e);
    }
  },

  saveSettings: function() {
    try {
      const settings = wx.getStorageSync('appSettings') || {};
      settings.hidePasswordList = this.data.hidePasswordList;
      settings.allowSaveActualPassword = this.data.allowSaveActualPassword;

      wx.setStorageSync('appSettings', settings);
    } catch (e) {
      console.error('Failed to save settings:', e);
    }
  },

  handleHidePasswordListChange: function(e) {
    const value = e.detail.value;
    this.setData({ hidePasswordList: value });

    if (!value) {
      // 如果关闭隐藏功能，清除访问手势密码
      try {
        const settings = wx.getStorageSync('appSettings') || {};
        delete settings.accessGesturePassword;
        wx.setStorageSync('appSettings', settings);
        this.setData({ accessGestureSet: false });
      } catch (e) {
        console.error('Failed to clear access gesture:', e);
      }
    }

    this.saveSettings();
  },

  handleAllowSaveActualPasswordChange: function(e) {
    const value = e.detail.value;
    this.setData({ allowSaveActualPassword: value });
    this.saveSettings();
  },

  setAccessGesture: function() {
    this.setData({
      showGestureModal: true,
      isConfirmingAccess: false,
      accessGesturePassword: [],
      confirmAccessGesturePassword: [],
      accessGestureComplete: false,
      showError: false,
      errorMessage: ''
    });

    // 初始化canvas
    setTimeout(() => {
      this.initGestureCanvas();
    }, 100);
  },

  initCanvas: function() {
    // Calculate canvas dimensions
    const windowWidth = wx.getSystemInfoSync().windowWidth;
    const canvasSize = Math.min(windowWidth * 0.6, 400); // Canvas will be 60% of screen width, max 400rpx

    this.setData({
      canvasWidth: canvasSize,
      canvasHeight: canvasSize
    });
  },

  initGestureCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#access-gesture-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 获取canvas的实际渲染尺寸
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = this.data.canvasWidth * dpr;
        canvas.height = this.data.canvasHeight * dpr;

        // 设置canvas的样式尺寸
        canvas.style = {
          width: this.data.canvasWidth + 'px',
          height: this.data.canvasHeight + 'px'
        };

        // 缩放绘图上下文，使得1个单位等于1个物理像素
        ctx.scale(dpr, dpr);

        this.canvas = canvas;
        this.ctx = ctx;

        // Initialize the grid points
        this.initializePoints();
        this.drawGestureGrid();
      });
  },

  initializePoints: function() {
    const { gridSize, canvasWidth, canvasHeight, pointRadius } = this.data;
    const points = [];

    // Calculate the space between points
    const spacingX = canvasWidth / gridSize;
    const spacingY = canvasHeight / gridSize;

    // Calculate the offset to center the points
    const offsetX = spacingX / 2;
    const offsetY = spacingY / 2;

    // Create the grid of points
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        points.push({
          x: offsetX + j * spacingX,
          y: offsetY + i * spacingY,
          id: i * gridSize + j + 1, // Point ID (1-9)
          touched: false
        });
      }
    }

    this.setData({ points });
  },

  drawGestureGrid: function() {
    const { points, pointRadius, drawnLines, currentLineColor } = this.data;
    const ctx = this.ctx;

    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

    // Draw the grid points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, pointRadius, 0, Math.PI * 2);
      ctx.fillStyle = point.touched ? currentLineColor : '#E0E0E0';
      ctx.fill();
      ctx.strokeStyle = '#CCCCCC';
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Draw the connecting lines between touched points
    if (drawnLines.length > 0) {
      ctx.beginPath();
      ctx.moveTo(drawnLines[0].x, drawnLines[0].y);

      for (let i = 1; i < drawnLines.length; i++) {
        ctx.lineTo(drawnLines[i].x, drawnLines[i].y);
      }

      // If there is a last point that is not part of the drawn lines yet
      if (this.data.lastPoint) {
        ctx.lineTo(this.data.lastPoint.x, this.data.lastPoint.y);
      }

      ctx.strokeStyle = currentLineColor;
      ctx.lineWidth = 3;
      ctx.stroke();
    }
  },

  handleTouchStart: function(e) {
    if (this.data.accessGestureComplete) return;

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#access-gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Check if the touch started on a point
        const point = this.findTouchedPoint(x, y);

        if (point) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Start drawing lines
          const drawnLines = [{ x: point.x, y: point.y }];

          // Add the point to gesture password
          const gesturePassword = this.data.isConfirmingAccess
            ? [...this.data.confirmAccessGesturePassword, point.id]
            : [...this.data.accessGesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            lastPoint: null,
            ...(this.data.isConfirmingAccess
              ? { confirmAccessGesturePassword: gesturePassword }
              : { accessGesturePassword: gesturePassword })
          });

          this.drawGestureGrid();
        }
      });
  },

  handleTouchMove: function(e) {
    if (this.data.accessGestureComplete) return;
    if (this.data.drawnLines.length === 0) return; // No touch start yet

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#access-gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Update last point for smooth line drawing
        this.setData({
          lastPoint: { x: x, y: y }
        });

        // Check if we've touched a new point
        const point = this.findTouchedPoint(x, y);

        if (point && !this.isPointAlreadyTouched(point.id)) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Add to drawn lines
          const drawnLines = [...this.data.drawnLines, { x: point.x, y: point.y }];

          // Add the point to gesture password
          const gesturePassword = this.data.isConfirmingAccess
            ? [...this.data.confirmAccessGesturePassword, point.id]
            : [...this.data.accessGesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            ...(this.data.isConfirmingAccess
              ? { confirmAccessGesturePassword: gesturePassword }
              : { accessGesturePassword: gesturePassword })
          });
        }

        this.drawGestureGrid();
      });
  },

  handleTouchEnd: function() {
    if (this.data.accessGestureComplete) return;

    // Check if the gesture is valid (at least 4 points)
    const passwordArray = this.data.isConfirmingAccess
      ? this.data.confirmAccessGesturePassword
      : this.data.accessGesturePassword;

    if (passwordArray.length < 4) {
      this.setData({
        showError: true,
        errorMessage: '手势密码至少需要连接 4 个点',
        currentLineColor: '#FF3B30'
      });

      this.drawGestureGrid();

      // Reset after a delay
      setTimeout(() => {
        this.resetGesture();
      }, 1500);

      return;
    }

    // If we're confirming the gesture
    if (this.data.isConfirmingAccess) {
      // Check if the confirmation matches the original gesture
      if (this.arraysEqual(this.data.accessGesturePassword, this.data.confirmAccessGesturePassword)) {
        this.setData({
          accessGestureComplete: true,
          currentLineColor: '#34C759',
          showError: false
        });

        this.drawGestureGrid();

        // Alert the user that the gesture was set successfully
        wx.showToast({
          title: '访问手势密码设置成功',
          icon: 'success'
        });
      } else {
        // Gestures don't match
        this.setData({
          showError: true,
          errorMessage: '手势密码不匹配，请重试',
          currentLineColor: '#FF3B30'
        });

        this.drawGestureGrid();

        // Reset after a delay
        setTimeout(() => {
          this.resetGesture();
          this.setData({
            isConfirmingAccess: false,
            accessGesturePassword: [],
            confirmAccessGesturePassword: []
          });
        }, 1500);
      }
    } else {
      // First time setting the gesture, now ask to confirm
      this.setData({
        isConfirmingAccess: true,
        showError: false
      });

      // Reset the canvas for confirmation
      setTimeout(() => {
        this.resetGesture();
      }, 500);
    }
  },

  findTouchedPoint: function(x, y) {
    const { points, pointRadius } = this.data;

    // Check if the touch is within any point's radius
    for (const point of points) {
      const distance = Math.sqrt(
        Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)
      );

      if (distance <= pointRadius * 1.5) { // A bit larger area for easier touch
        return point;
      }
    }

    return null;
  },

  isPointAlreadyTouched: function(pointId) {
    const passwordArray = this.data.isConfirmingAccess
      ? this.data.confirmAccessGesturePassword
      : this.data.accessGesturePassword;

    return passwordArray.includes(pointId);
  },

  resetGesture: function() {
    // Reset the grid points
    const updatedPoints = this.data.points.map(p => ({ ...p, touched: false }));

    this.setData({
      points: updatedPoints,
      drawnLines: [],
      lastPoint: null,
      showError: false,
      currentLineColor: '#007AFF',
      ...(this.data.isConfirmingAccess ? {} : { accessGesturePassword: [] }),
      ...(this.data.isConfirmingAccess ? { confirmAccessGesturePassword: [] } : {})
    });

    this.drawGestureGrid();
  },

  arraysEqual: function(a, b) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (a[i] !== b[i]) return false;
    }
    return true;
  },

  cancelGestureSetup: function() {
    this.setData({
      showGestureModal: false,
      isConfirmingAccess: false,
      accessGesturePassword: [],
      confirmAccessGesturePassword: [],
      accessGestureComplete: false,
      showError: false,
      errorMessage: ''
    });
  },

  confirmGestureSetup: function() {
    try {
      const settings = wx.getStorageSync('appSettings') || {};
      settings.accessGesturePassword = this.data.accessGesturePassword;
      wx.setStorageSync('appSettings', settings);

      this.setData({
        showGestureModal: false,
        accessGestureSet: true,
        isConfirmingAccess: false,
        accessGesturePassword: [],
        confirmAccessGesturePassword: [],
        accessGestureComplete: false,
        showError: false,
        errorMessage: ''
      });

      wx.showToast({
        title: '访问手势密码已保存',
        icon: 'success'
      });
    } catch (e) {
      console.error('Failed to save access gesture:', e);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  }
})