<!--pages/index/index.wxml-->
<view class="page">
  <view class="header">
    <view class="security-notice">
      <view class="security-icon">🔒</view>
      <view class="security-text">密码精灵不会存储您的实际密码，只保存用于生成密码的元数据</view>
    </view>
    <view class="settings-button" bindtap="navigateToSettings">
      <text class="settings-icon">⚙️</text>
    </view>
  </view>

  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input class="search-input" placeholder="搜索URL或标签" bindinput="handleSearchInput" value="{{searchText}}" confirm-type="search"/>
      <text class="clear-icon" bindtap="clearSearch" wx:if="{{searchText}}">✕</text>
    </view>
  </view>

  <!-- 手势密码认证界面 -->
  <view class="auth-container" wx:if="{{needAuth && !isAuthenticated}}">
    <view class="auth-title">请输入手势密码以查看密码列表</view>

    <!-- Gesture Pattern Canvas -->
    <canvas type="2d" id="auth-gesture-canvas" class="auth-gesture-canvas"
            disable-scroll="true"
            bindtouchstart="handleAuthTouchStart"
            bindtouchmove="handleAuthTouchMove"
            bindtouchend="handleAuthTouchEnd">
    </canvas>

    <view wx:if="{{showAuthError}}" class="auth-error-message">{{authErrorMessage}}</view>
    <view class="auth-hint">请连接至少 4 个点</view>
  </view>

  <!-- 密码列表 -->
  <view class="password-list" wx:if="{{!needAuth || isAuthenticated}}">
    <block wx:if="{{passwordEntries.length > 0}}">
      <view class="password-item" wx:for="{{passwordEntries}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="password-item-info">
          <view class="site-name">{{item.site}}</view>
          <view class="username">{{item.username}}</view>
        </view>
        <view class="arrow">></view>
      </view>
    </block>

    <view wx:else class="empty-state">
      <view class="empty-text">您还没有保存任何密码</view>
    </view>
  </view>

  <view class="add-button" bindtap="navigateToAdd">
    <text class="add-icon">+</text>
    <text class="add-text">添加新密码</text>
  </view>
</view>
