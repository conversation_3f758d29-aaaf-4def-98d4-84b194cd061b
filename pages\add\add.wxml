<!--pages/add/add.wxml-->
<view class="page">
  <view class="header">
    <view class="title">新增密码管理</view>
  </view>

  <view class="form-container">
    <view class="form-group">
      <view class="form-label">URL或者标签</view>
      <input class="input-item" placeholder="请输入URL或者标签" value="{{site}}" bindinput="handleSiteInput" />
      <view class="uri-removed-tip" wx:if="{{showUriRemovedTip}}">
        <text class="tip-icon">ℹ️</text>
        <text class="tip-text">已自动移除URI部分，仅保留域名以确保生成一致的密码</text>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">用户名</view>
      <input class="input-item" placeholder="请输入用户名" value="{{username}}" bindinput="handleUsernameInput" />
    </view>



    <!-- 密码模式选择 -->
    <view class="form-group" wx:if="{{allowSaveActualPassword}}">
      <view class="form-label">密码模式</view>
      <radio-group class="password-mode-group" bindchange="handlePasswordModeChange">
        <label class="password-mode-item">
          <radio value="generate" checked="{{passwordMode === 'generate'}}" color="#007AFF" />生成密码
        </label>
        <label class="password-mode-item">
          <radio value="save" checked="{{passwordMode === 'save'}}" color="#007AFF" />保存现有密码
        </label>
      </radio-group>
    </view>

    <!-- 现有密码输入 -->
    <view class="form-group" wx:if="{{passwordMode === 'save'}}">
      <view class="form-label">现有密码</view>
      <input class="input-item" placeholder="请输入现有密码" value="{{existingPassword}}" bindinput="handleExistingPasswordInput" password="{{!showExistingPassword}}" />
      <view class="password-toggle" bindtap="toggleExistingPasswordVisibility">
        <text class="toggle-icon">{{showExistingPassword ? '🙈' : '👁️'}}</text>
      </view>
    </view>

    <view class="form-group" wx:if="{{passwordMode === 'generate'}}">
      <view class="form-label">密码长度: {{passwordLength}}</view>
      <slider min="6" max="32" value="{{passwordLength}}" show-value activeColor="#007AFF" block-size="28" bind:change="handleLengthChange" />
    </view>

    <view class="form-group" wx:if="{{passwordMode === 'generate'}}">
      <view class="form-label">密码类型</view>
      <picker bindchange="handlePasswordTypeChange" value="{{passwordTypeIndex}}" range="{{passwordTypes}}" range-key="label">
        <view class="picker-item">
          <text>{{passwordTypes[passwordTypeIndex].label}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <view class="gesture-container">
      <view class="gesture-title" wx:if="{{!showPassword && !showSavedPassword}}">
        {{isConfirming ? '请再次输入手势密码以确认' : (passwordMode === 'save' ? '请设置手势密码以保护您的密码' : '请设置手势密码')}}
      </view>

      <!-- Gesture Pattern Canvas -->
      <canvas type="2d" id="gesture-canvas" class="gesture-canvas"
              disable-scroll="true"
              bindtouchstart="handleTouchStart"
              bindtouchmove="handleTouchMove"
              bindtouchend="handleTouchEnd">
      </canvas>

      <view wx:if="{{showError}}" class="error-message">{{errorMessage}}</view>
      <view wx:if="{{gestureComplete && !showPassword}}" class="success-message">手势密码设置成功</view>

      <view class="gesture-hint" wx:if="{{!showPassword}}">请连接至少 4 个点</view>

      <view wx:if="{{!isConfirming && gesturePassword.length === 0 && !showPassword}}" class="reset-button" bindtap="resetAll">重置</view>

      <!-- Password display view -->
      <view class="password-section" wx:if="{{showPassword}}">
        <view class="section-title">生成的密码</view>
        <view class="display-password">{{generatedPassword}}</view>

        <view class="password-actions">
          <button class="btn-action" bindtap="copyPassword">复制密码</button>
        </view>
      </view>

      <!-- Saved password display view -->
      <view class="password-section" wx:if="{{showSavedPassword}}">
        <view class="section-title">已保存的密码</view>
        <view class="saved-password-info">
          <text class="info-icon">✅</text>
          <text class="info-text">您的密码已安全保存，需要手势密码验证才能查看</text>
        </view>
      </view>
    </view>

    <view class="security-explanation">
      <view class="security-title">
        <text class="security-icon">🔒</text> 安全说明
      </view>
      <view class="security-content" wx:if="{{passwordMode === 'generate'}}">
        密码精灵不会存储您生成的密码，只保存用于生成密码的元数据。密码仅在您输入手势密码后在本地生成。
      </view>
      <view class="security-content" wx:if="{{passwordMode === 'save'}}">
        您的实际密码将被加密保存，只有通过手势密码验证才能查看。请确保您的手势密码足够安全。
      </view>
    </view>

    <view class="btn-container">
      <button class="btn-primary {{!gestureComplete ? 'btn-disabled' : ''}}" disabled="{{!gestureComplete}}" bindtap="savePasswordEntry">
        {{passwordMode === 'save' ? '保存密码' : '保存密码'}}
      </button>
      <button class="btn-secondary" bindtap="resetAll">重新设置</button>
    </view>
  </view>
</view>
